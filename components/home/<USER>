"use client";

import React, { useState, useEffect, useRef } from "react";
import Wrapper from "@/components/Wrapper";
import Marquee from "react-fast-marquee";

const logos = [
  "/img1.jpg",
  "/img2.png",
  "/img3.webp",
  "/img4.webp",
  "/img5.webp",
  "/img6.webp",
  "/img7.webp",
  "/img8.jpg",
];

const AnimatedCounter = ({ target, duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setHasAnimated(true);

          let startTime;
          let animationId;

          const animate = (currentTime) => {
            if (!startTime) startTime = currentTime;
            const progress = Math.min((currentTime - startTime) / duration, 1);

            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            setCount(Math.floor(easeOutQuart * target));

            if (progress < 1) {
              animationId = requestAnimationFrame(animate);
            }
          };

          animationId = requestAnimationFrame(animate);

          return () => {
            if (animationId) {
              cancelAnimationFrame(animationId);
            }
          };
        }
      },
      { threshold: 0.3 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [target, duration, hasAnimated]);

  return <span ref={counterRef} className="font-bold text-blue-600">{count}</span>;
};

const TrustedBy = () => (
  <Wrapper padding="default" background="transparent">
    <section className="w-full flex flex-col items-center py-8 md:py-12">
      <h4 className="text-gray-600 font-bold mb-8 md:mb-12 text-center text-lg md:text-xl lg:text-2xl tracking-wider uppercase">
        TRUSTED BY MORE THAN <AnimatedCounter target={100} />+ COMPANIES WORLDWIDE
      </h4>

      <div className="w-full overflow-hidden">
        <Marquee
          gradient={false}
          speed={50}
          pauseOnHover={true}
          className="py-4"
        >
          {logos.map((logo, idx) => (
            <div key={idx} className="mx-8 md:mx-12 flex items-center justify-center">
              <img
                src={logo}
                alt={`Trusted company logo ${idx + 1}`}
                className="h-10 md:h-14 lg:h-16 object-contain transition-transform duration-300 hover:scale-110"
              />
            </div>
          ))}
        </Marquee>
      </div>
    </section>
  </Wrapper>
);

export default TrustedBy;
