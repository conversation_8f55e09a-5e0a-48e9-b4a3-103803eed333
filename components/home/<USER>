import React, { useState, useEffect, useRef } from "react";
import Wrapper from "@/components/Wrapper";
import Marquee from "react-fast-marquee";

const logos = [
  "/img1.jpg",
  "/img2.png",
  "/img3.webp",
  "/img4.webp",
  "/img5.webp",
  "/img6.webp",
  "/img7.webp",
  "/img8.jpg",
];

const AnimatedCounter = ({ target, duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime;
    const animate = (currentTime) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      setCount(Math.floor(progress * target));

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [isVisible, target, duration]);

  return <span ref={counterRef}>{count}</span>;
};

const TrustedBy = () => (
  <Wrapper padding="default" background="transparent">
    <section className="w-full flex flex-col items-center py-8 md:py-12">
      <h4 className="text-gray-700 font-semibold mb-8 md:mb-12 text-center text-sm md:text-base tracking-wide">
        TRUSTED BY MORE THAN <AnimatedCounter target={100} />+ COMPANIES WORLDWIDE
      </h4>

      <div className="w-full overflow-hidden">
        <Marquee
          gradient={false}
          speed={50}
          pauseOnHover={true}
          className="py-4"
        >
          {logos.map((logo, idx) => (
            <div key={idx} className="mx-8 md:mx-12 flex items-center justify-center">
              <img
                src={logo}
                alt={`Trusted company logo ${idx + 1}`}
                className="h-8 md:h-12 lg:h-16 object-contain grayscale hover:grayscale-0 transition-all duration-300 opacity-70 hover:opacity-100"
              />
            </div>
          ))}
        </Marquee>
      </div>
    </section>
  </Wrapper>
);

export default TrustedBy;
